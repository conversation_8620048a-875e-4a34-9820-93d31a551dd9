# 🗄️ دليل قاعدة البيانات - Mega Platform

## 📋 فهرس المحتويات
- [نظرة عامة](#نظرة-عامة)
- [مخطط قاعدة البيانات](#مخطط-قاعدة-البيانات)
- [الجداول الأساسية](#الجداول-الأساسية)
- [العلاقات بين الجداول](#العلاقات-بين-الجداول)
- [الفهارس والقيود](#الفهارس-والقيود)
- [أمثلة الاستعلامات](#أمثلة-الاستعلامات)

---

## 🌟 نظرة عامة

قاعدة البيانات مصممة لدعم نظام تجارة إلكترونية متكامل مع:
- **18 جدول** رئيسي
- **علاقات معقدة** بين الكيانات
- **فهارس محسنة** للأداء
- **قيود تكامل البيانات**

### إحصائيات قاعدة البيانات
- **Users**: 4 أنواع (user, provider, driver, admin)
- **Products**: مع دعم الصور المتعددة
- **Categories**: هيكل هرمي (parent/child)
- **Orders**: نظام طلبات متكامل مع التوصيل
- **Ratings**: نظام تقييمات ثنائي الاتجاه

---

## 📊 مخطط قاعدة البيانات

```mermaid
erDiagram
    users ||--o{ listings : "يملك"
    users ||--o{ orders : "يطلب"
    users ||--o{ orders : "يوفر"
    users ||--o{ orders : "يوصل"
    users ||--o{ ratings : "يقيم"
    users ||--o{ ratings : "يُقيم"
    users ||--o{ transactions : "ينفذ"
    users ||--o{ drivers : "ملف_سائق"
    
    categories ||--o{ categories : "فئة_فرعية"
    categories ||--o{ listings : "تصنف"
    
    listings ||--o{ listing_images : "صور"
    listings ||--o{ orders : "يُطلب"
    listings ||--o{ ratings : "يُقيم"
    listings ||--o{ wishlists : "مفضل"
    
    orders ||--o{ transactions : "دفع"
    orders ||--o{ ratings : "تقييم_طلب"
    
    users {
        bigint id PK
        string name
        string email UK
        string phone
        timestamp email_verified_at
        string password
        enum type
        boolean is_verified
        timestamp created_at
        timestamp updated_at
    }
    
    categories {
        bigint id PK
        string name
        bigint parent_id FK
        string icon
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    listings {
        bigint id PK
        bigint user_id FK
        string title
        text description
        bigint category_id FK
        string country
        string city
        string location
        decimal price
        boolean is_promoted
        enum status
        timestamp promoted_until
        integer views
        timestamp created_at
        timestamp updated_at
    }
```

---

## 📋 الجداول الأساسية

### 1. جدول المستخدمين (users)

```sql
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(255) NULL,
    email_verified_at TIMESTAMP NULL,
    password VARCHAR(255) NOT NULL,
    type ENUM('user', 'provider', 'driver', 'admin') DEFAULT 'user',
    is_verified BOOLEAN DEFAULT FALSE,
    remember_token VARCHAR(100) NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

**الحقول:**
- `id`: المعرف الفريد
- `name`: اسم المستخدم
- `email`: البريد الإلكتروني (فريد)
- `phone`: رقم الهاتف
- `type`: نوع المستخدم (user/provider/driver/admin)
- `is_verified`: حالة التحقق

### 2. جدول الفئات (categories)

```sql
CREATE TABLE categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    parent_id BIGINT UNSIGNED NULL,
    icon VARCHAR(255) NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
);
```

**الحقول:**
- `id`: المعرف الفريد
- `name`: اسم الفئة
- `parent_id`: الفئة الأب (للفئات الفرعية)
- `icon`: أيقونة الفئة
- `is_active`: حالة النشاط

### 3. جدول المنتجات (listings)

```sql
CREATE TABLE listings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category_id BIGINT UNSIGNED NOT NULL,
    country VARCHAR(255) NOT NULL,
    city VARCHAR(255) NOT NULL,
    location VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    is_promoted BOOLEAN DEFAULT FALSE,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    promoted_until TIMESTAMP NULL,
    views INTEGER DEFAULT 0,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
);
```

**الحقول:**
- `user_id`: معرف البائع
- `title`: عنوان المنتج
- `description`: وصف المنتج
- `category_id`: معرف الفئة
- `price`: السعر
- `status`: حالة الموافقة
- `is_promoted`: منتج مميز

### 4. جدول صور المنتجات (listing_images)

```sql
CREATE TABLE listing_images (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    listing_id BIGINT UNSIGNED NOT NULL,
    image_url VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (listing_id) REFERENCES listings(id) ON DELETE CASCADE
);
```

### 5. جدول الطلبات (orders)

```sql
CREATE TABLE orders (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    listing_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NOT NULL,
    provider_id BIGINT UNSIGNED NOT NULL,
    driver_id BIGINT UNSIGNED NULL,
    status ENUM('pending', 'accepted', 'in_delivery', 'delivered', 'cancelled') DEFAULT 'pending',
    delivery_address TEXT NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (listing_id) REFERENCES listings(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (provider_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (driver_id) REFERENCES users(id) ON DELETE SET NULL
);
```

### 6. جدول التقييمات (ratings)

```sql
CREATE TABLE ratings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    reviewer_id BIGINT UNSIGNED NOT NULL,
    reviewed_id BIGINT UNSIGNED NOT NULL,
    listing_id BIGINT UNSIGNED NULL,
    order_id BIGINT UNSIGNED NULL,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (listing_id) REFERENCES listings(id) ON DELETE SET NULL,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL
);
```

### 7. جدول المعاملات المالية (transactions)

```sql
CREATE TABLE transactions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    type ENUM('payment', 'refund', 'commission') NOT NULL,
    status ENUM('pending', 'success', 'failed') DEFAULT 'pending',
    payment_method VARCHAR(255) NULL,
    transaction_reference VARCHAR(255) NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### 8. جدول قائمة الأمنيات (wishlists)

```sql
CREATE TABLE wishlists (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    listing_id BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (listing_id) REFERENCES listings(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_listing (user_id, listing_id)
);
```

---

## 🔗 العلاقات بين الجداول

### علاقات One-to-Many

1. **User → Listings**
   - مستخدم واحد يمكن أن يملك عدة منتجات
   ```php
   // في User Model
   public function listings() {
       return $this->hasMany(Listing::class);
   }
   ```

2. **Category → Listings**
   - فئة واحدة تحتوي على عدة منتجات
   ```php
   // في Category Model
   public function listings() {
       return $this->hasMany(Listing::class);
   }
   ```

3. **Listing → Images**
   - منتج واحد يمكن أن يحتوي على عدة صور
   ```php
   // في Listing Model
   public function images() {
       return $this->hasMany(ListingImage::class);
   }
   ```

### علاقات Many-to-Many

1. **User ↔ Listings (Wishlist)**
   - مستخدم يمكن أن يضع عدة منتجات في المفضلة
   - منتج يمكن أن يكون في مفضلة عدة مستخدمين
   ```php
   // في User Model
   public function wishlist() {
       return $this->belongsToMany(Listing::class, 'wishlists');
   }
   ```

### علاقات Self-Referencing

1. **Category → Category (Parent/Child)**
   ```php
   // في Category Model
   public function parent() {
       return $this->belongsTo(Category::class, 'parent_id');
   }
   
   public function children() {
       return $this->hasMany(Category::class, 'parent_id');
   }
   ```

---

## 📈 الفهارس والقيود

### الفهارس المطبقة

```sql
-- فهارس الأداء
CREATE INDEX idx_listings_category ON listings(category_id);
CREATE INDEX idx_listings_user ON listings(user_id);
CREATE INDEX idx_listings_status ON listings(status);
CREATE INDEX idx_listings_promoted ON listings(is_promoted);
CREATE INDEX idx_listings_city ON listings(city);
CREATE INDEX idx_listings_price ON listings(price);

-- فهارس البحث
CREATE INDEX idx_listings_title ON listings(title);
CREATE FULLTEXT INDEX idx_listings_search ON listings(title, description);

-- فهارس الطلبات
CREATE INDEX idx_orders_user ON orders(user_id);
CREATE INDEX idx_orders_provider ON orders(provider_id);
CREATE INDEX idx_orders_driver ON orders(driver_id);
CREATE INDEX idx_orders_status ON orders(status);

-- فهارس التقييمات
CREATE INDEX idx_ratings_reviewer ON ratings(reviewer_id);
CREATE INDEX idx_ratings_reviewed ON ratings(reviewed_id);
CREATE INDEX idx_ratings_listing ON ratings(listing_id);
```

### القيود المطبقة

```sql
-- قيود التكامل
ALTER TABLE listings ADD CONSTRAINT chk_price_positive CHECK (price > 0);
ALTER TABLE ratings ADD CONSTRAINT chk_rating_range CHECK (rating BETWEEN 1 AND 5);
ALTER TABLE transactions ADD CONSTRAINT chk_amount_positive CHECK (amount > 0);

-- قيود الفرادة
ALTER TABLE wishlists ADD CONSTRAINT uk_user_listing UNIQUE (user_id, listing_id);
ALTER TABLE users ADD CONSTRAINT uk_email UNIQUE (email);
```
