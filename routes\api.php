<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\ListingController;
use App\Http\Controllers\CategoryController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

 Route::get('/networkCatAPI', [CategoryController::class, 'networkCatAPI']);
Route::get('categories', [CategoryController::class, 'getCategories']);
Route::get('products/featured', [ListingController::class, 'getFeaturedProducts']);
Route::get('/categories/{category}/listings', [CategoryController::class, 'relatedProducts']);

// Auth Routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Protected Routes (تتطلب Token مصادقة)
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [AuthController::class, 'user']);
        Route::get('provider/products', [\App\Http\Controllers\ListingController::class, 'providerProducts']);
            Route::post('provider/products', [\App\Http\Controllers\ListingController::class, 'storeAPI']);

          Route::delete('provider/products/{id}', [\App\Http\Controllers\ListingController::class, 'destroyApi']);


    // هنا تضيف باقي الـ API المحمية مثل:
    // Route::apiResource('products', ProductController::class);
});

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// ⚠️ SECURITY FIX: Removed dangerous /destroy-project endpoint
// This endpoint was capable of deleting the entire project
// Removed on: January 12, 2025
// Reason: Critical security vulnerability
